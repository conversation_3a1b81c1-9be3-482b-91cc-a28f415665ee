from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db.tables import tables

from .base import BaseModel, NonForeignKeyAuditMixin, primary_key


class Department(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.departments

	id = primary_key()
	name = Column(String(50), nullable=False, unique=True)
	code = Column(String(10), nullable=False, unique=True)
	description =  Column(String(255), nullable=True, unique=False)


	# # Relationships
	users = relationship(
		"User",
		secondary=tables.user_departments,
		back_populates="departments",
  	primaryjoin='Department.id == UserDepartment.department_id',
		secondaryjoin='User.id == UserDepartment.user_id',
		viewonly=True
	)

	def __repr__(self):
		return f"<Department(id={self.id}, name={self.name}, code={self.code})>"
