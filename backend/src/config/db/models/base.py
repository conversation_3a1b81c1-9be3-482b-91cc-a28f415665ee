import uuid
from enum import Enum

from sqlalchemy import T<PERSON><PERSON><PERSON>MP, <PERSON><PERSON><PERSON>, Column, Foreign<PERSON>ey, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

def timestamp_column(nullable=False, timezone=True, use_default=False):
	if use_default:
		return Column(TIMESTAMP(timezone=timezone), server_default=func.now(), nullable=nullable)
	return Column(TIMESTAMP(timezone=timezone), nullable=nullable)

def uuid_column(nullable=True, set_default=False):
	return Column(UUID(as_uuid=True), default=uuid.uuid4 if set_default else None, unique=False, nullable=nullable)


def primary_key() -> Column[UUID]:
	return Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)


def foreign_key(reference: str, nullable=False, primary_key=False) -> Column[UUID]:
	return Column(UUID(as_uuid=True), ForeignKey(reference), nullable=nullable, primary_key=primary_key)


class BasicStatus(str, Enum):
	ACTIVE = "ACTIVE"
	INACTIVE = "INACTIVE"


class Gender(str, Enum):
	MALE = "MALE"
	FEMALE = "FEMALE"


class InvitationStatus(str, Enum):
	PENDING = "PENDING"
	ACCEPTED = "ACCEPTED"
	REJECTED = "REJECTED"


class NonForeignKeyAuditMixin:
	created_by = uuid_column()
	updated_by = uuid_column()
	voided = Column(Boolean, default=False, nullable=False)
	voided_by = uuid_column()
	void_reason = Column(Text, nullable=True)


class AuditMixin:
	created_by = foreign_key("users.id", nullable=True)
	updated_by = foreign_key("users.id", nullable=True)
	voided = Column(Boolean, default=False, nullable=False)
	voided_by = foreign_key("users.id", nullable=True)
	void_reason = Column(Text, nullable=True)

	@declared_attr
	def created_by_user(cls):
		return relationship("User", foreign_keys=[cls.created_by], overlaps="_created_by_user")

	@declared_attr
	def updated_by_user(cls):
		return relationship("User", foreign_keys=[cls.updated_by], overlaps="_updated_by_user")

	@declared_attr
	def voided_by_user(cls):
		return relationship("User", foreign_keys=[cls.voided_by], overlaps="_voided_by_user")


class TimestampMixin:
	"""Mixin class for common timestamp columns"""
	created_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)
	updated_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)


class BaseModel(Base, TimestampMixin):
	"""Abstract base model with common fields"""
	__abstract__ = True
