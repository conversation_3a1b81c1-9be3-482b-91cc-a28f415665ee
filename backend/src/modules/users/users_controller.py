from typing import List
from uuid import UUID
from fastapi import Depends, HTTPException, status
from fastapi_pagination.ext.sqlalchemy import paginate
from src.core.exceptions.api import ApiException
from src.modules.auth.auth_schema import UserCreate
from src.modules.auth.auth_service import AuthService
from src.modules.users.users_schema import (
    UserResponse, UserFilters, UserUpdate,
    UserDepartmentAssignRequest, UserDepartmentsResponse,
    UserRoleAssignRequest, UserRolesResponse
)
from src.modules.users.users_service import UsersService

service = UsersService()
auth = AuthService()

def index(filters: UserFilters = Depends(UserFilters)) -> list[UserResponse]:
    return paginate(service.find_users(filters))

def create(user: UserCreate) -> UserResponse:
    return auth.register_user(user)

def update(id, updates: UserUpdate) -> UserResponse:
    return service.update_user(user_id=id, updates=updates)

def delete(user_id: UUID):
    return service.delete_user(user_id)

def assign_user_to_departments(request: UserDepartmentAssignRequest) -> UserDepartmentsResponse:
    """
    Assign a user to multiple departments.

    Args:
        request (UserDepartmentAssignRequest): Assignment request data.

    Returns:
        UserDepartmentsResponse: Assignment response data.
    """
    return service.assign_user_to_departments(request)    

def unassign_user_from_departments(user_id: UUID, department_ids: List[UUID] = None):
    """
    Unassign a user from all their departments.

    Args:
        user_id (UUID): User ID to unassign.
    """
    service.unassign_user_from_departments(user_id, department_ids)

def assign_roles_to_user(request: UserRoleAssignRequest) -> UserRolesResponse:
    """
    Assign multiple roles to a user.

    Args:
        request (UserRoleAssignRequest): Assignment request data.

    Returns:
        UserRolesResponse: Assignment response data.
    """
    return service.assign_roles_to_user(request)

def unassign_roles_from_user(user_id: UUID, role_ids: List[UUID] = None) -> None:
    """
    Unassign all roles from a user.

    Args:
        user_id (UUID): User ID to unassign roles from.
    """
    service.unassign_roles_from_user(user_id, role_ids)