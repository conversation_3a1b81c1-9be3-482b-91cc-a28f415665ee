from typing import List
from uuid import UUID
from src.core.exceptions.api import ApiException
from src.modules.users.users_schema import (
    UserResponse, UserFilters, UserUpdate,
    UserDepartmentAssignRequest, UserDepartmentResponse, UserDepartmentsResponse,
    UserRoleAssignRequest, UserRoleResponse, UserRolesResponse
)
from src.core.base.application_repository import ApplicationRepository
from src.config.db.models.user import User
from src.config.db.models.user_department import UserDepartment
from src.config.db.models.user_role import UserRole
from src.config.db.models.department import Department
from src.config.db.models.role import Role

class UsersService(ApplicationRepository):

    def __init__(self):
        super().__init__()

    def find_users(self, filters: UserFilters) -> list[UserResponse]:
        """
          Find users based on filters.

          Args:
            filters (dict): Dictionary of filters to apply.

          Returns:
            list: List of users matching the criteria.
        """
        user_filters = ['first_name', 'middle_name', 'last_name', 'email', 'is_external']

        account_filters = ['handle', 'type', 'status']

        try:
            ufilters: dict = {k: v for k,v in filters if v is not None and k in user_filters}
            afilters: dict = {k: v for k, v in filters if v is not None and k in account_filters}

            query = self.db.query(User)

            if ufilters:
                for field in ["first_name", "middle_name", "last_name"]:
                    if field in ufilters:
                        filter_value = ufilters.pop(field)
                        query = query.filter(
                            getattr(User, field).like(f"{filter_value}%")
                        )

                query = query.filter_by(**ufilters)
            if afilters:
                query = query.join(User.account).filter_by(**afilters)
            return query
        except Exception as e:
            raise ApiException("Failed to retrieve users")

    def update_user(self, user_id: UUID, updates: UserUpdate) -> UserResponse:
        try:                        
            user = self.db.query(User).filter_by(id=user_id).first()
            if user:
                for k, v in updates:
                    if v is not None: 
                      setattr(user, k, v)
            
            account = user.account
            if account:
              for k, v in updates:
                if v is not None:
                  setattr(account, k, v)
            else:
                raise ApiException('user not found')
            self.db.commit()
            self.db.refresh(user)
            return UserResponse.model_validate(user)
        except Exception as e:
            raise ApiException("Failed to update user")

    def delete_user(self, user_id) -> None:
      try:
          user = self.db.query(User).filter_by(id=user_id).first()
          if user.account.handle == 'root':
            return ApiException('Cannot delete superuser account')

          if user:
              self.db.delete(user)
              self.db.commit()
              return
          else:
              raise ApiException('user not found')
      except Exception as e:
          raise ApiException("Failed to delete user")

    def assign_user_to_departments(self, request: UserDepartmentAssignRequest) -> UserDepartmentsResponse:
        """
        Assign a user to multiple departments.

        Args:
            request (UserDepartmentAssignRequest): Assignment request data.

        Returns:
            UserDepartmentsResponse: Assignment response data.
        """
        try:
            # Validate user exists
            user = self.db.query(User).filter(User.id == request.user_id).first()
            if not user:
                raise ApiException('User not found')

            # Validate all departments exist
            departments = self.db.query(Department).filter(
                Department.id.in_(request.department_ids)
            ).all()

            if len(departments) != len(request.department_ids):
                found_ids = {dept.id for dept in departments}
                missing_ids = set(request.department_ids) - found_ids
                raise ApiException(f'Departments not found: {missing_ids}')

            assignments = []
            for department_id in request.department_ids:
                # Check if user already has this department assignment
                existing_assignment = self.db.query(UserDepartment).filter(
                    UserDepartment.user_id == request.user_id,
                    UserDepartment.department_id == department_id,
                ).first()

                if existing_assignment:
                    # Skip if already assigned
                    assignments.append(UserDepartmentResponse.model_validate(existing_assignment))
                    continue

                # Create new assignment
                assignment = UserDepartment(
                    user_id=request.user_id,
                    department_id=department_id
                )

                self.db.add(assignment)
                self.db.flush()  # Flush to get the ID
                assignments.append(UserDepartmentResponse.model_validate(assignment))

            self.db.commit()

            return UserDepartmentsResponse(
                user_id=request.user_id,
                assignments=assignments
            )

        except Exception as e:
            self.db.rollback()
            raise ApiException("Failed to assign user to departments")

    def unassign_user_from_departments(self, user_id: UUID, department_ids: List[UUID] = None) -> None:
        """
        Unassign a user from departments. If department_ids is None, unassign from all departments.

        Args:
            user_id (UUID): User ID to unassign.
            department_ids (List[UUID], optional): Specific department IDs to unassign from.
                                                   If None, unassign from all departments.
        """
        try:
            # Build query for assignments to unassign
            query = self.db.query(UserDepartment).filter(
                UserDepartment.user_id == user_id,
                UserDepartment.voided == False
            )

            if department_ids:
                query = query.filter(UserDepartment.department_id.in_(department_ids))

            assignments = query.all()

            if not assignments:
                if department_ids:
                    raise ApiException('User is not assigned to the specified departments')
                else:
                    raise ApiException('User is not assigned to any department')

            # Soft delete the assignments
            for assignment in assignments:
                assignment.voided = True
                assignment.voided_reason = "User unassigned from department"

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            raise ApiException("Failed to unassign user from departments")

    def assign_roles_to_user(self, request: UserRoleAssignRequest) -> UserRolesResponse:
        """
        Assign multiple roles to a user.

        Args:
            request (UserRoleAssignRequest): Assignment request data.

        Returns:
            UserRolesResponse: Assignment response data.
        """
        try:
            # Validate user exists
            user = self.db.query(User).filter(User.id == request.user_id).first()
            if not user:
                raise ApiException('User not found')

            # Validate all roles exist
            roles = self.db.query(Role).filter(
                Role.id.in_(request.role_ids)
            ).all()

            if len(roles) != len(request.role_ids):
                found_ids = {role.id for role in roles}
                missing_ids = set(request.role_ids) - found_ids
                raise ApiException(f'Roles not found: {missing_ids}')

            assignments = []
            for role_id in request.role_ids:
                # Check if user already has this role assignment
                existing_assignment = self.db.query(UserRole).filter(
                    UserRole.user_id == request.user_id,
                    UserRole.role_id == role_id,
                    UserRole.voided == False
                ).first()

                if existing_assignment:
                    # Skip if already assigned
                    assignments.append(UserRoleResponse.model_validate(existing_assignment))
                    continue

                # Create new assignment
                assignment = UserRole(
                    user_id=request.user_id,
                    role_id=role_id
                )

                self.db.add(assignment)
                self.db.flush()  # Flush to get the ID
                assignments.append(UserRoleResponse.model_validate(assignment))

            self.db.commit()

            return UserRolesResponse(
                user_id=request.user_id,
                assignments=assignments
            )

        except Exception as e:
            self.db.rollback()
            raise ApiException("Failed to assign roles to user")

    def unassign_roles_from_user(self, user_id: UUID, role_ids: List[UUID] = None) -> None:
        """
        Unassign roles from a user. If role_ids is None, unassign all roles.

        Args:
            user_id (UUID): User ID to unassign roles from.
            role_ids (List[UUID], optional): Specific role IDs to unassign.
                                            If None, unassign all roles.
        """
        try:
            # Build query for assignments to unassign
            query = self.db.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.voided == False
            )

            if role_ids:
                query = query.filter(UserRole.role_id.in_(role_ids))

            assignments = query.all()

            if not assignments:
                if role_ids:
                    raise ApiException('User does not have the specified roles assigned')
                else:
                    raise ApiException('User does not have any roles assigned')

            # Soft delete the assignments
            for assignment in assignments:
                assignment.voided = True
                assignment.voided_reason = "Role unassigned from user"

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            raise ApiException("Failed to unassign roles from user")
