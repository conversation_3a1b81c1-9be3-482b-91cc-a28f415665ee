from typing import Any
from uuid import UUID
from fastapi import Depends, HTTPException, status
from fastapi_pagination.ext.sqlalchemy import paginate
from src.core.exceptions.api import ApiException
from src.modules.roles.roles_schema import RoleResponse, RoleFilters, RoleCreate, RoleUpdate
from src.modules.roles.roles_service import RolesService

service = RolesService()


def index(filters: RoleFilters = Depends(RoleFilters)) -> list[RoleResponse]:
    """
    Get and search for roles with pagination.
    
    Args:
        filters (RoleFilters): Query filters for roles.
        
    Returns:
        Page[RoleResponse]: Paginated list of roles.
    """
    try:
        return paginate(service.find_roles(filters))
    except ApiException as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def create(role: RoleCreate) -> RoleResponse:
    """
    Create a new role.
    
    Args:
        role (RoleCreate): Role data to create.
        
    Returns:
        RoleResponse: Created role data.
    """
    try:
        return service.create_role(role)
    except ApiException as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def get_by_id(role_id: UUID) -> RoleResponse:
    """
    Get a role by ID.
    
    Args:
        role_id (UUID): Role ID.
        
    Returns:
        RoleResponse: Role data.
    """
    try:
        return service.get_role_by_id(role_id)
    except ApiException as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def update(role_id: UUID, updates: RoleUpdate) -> RoleResponse:
    """
    Update a role.
    
    Args:
        role_id (UUID): Role ID to update.
        updates (RoleUpdate): Updates to apply.
        
    Returns:
        RoleResponse: Updated role data.
    """
    try:
        return service.update_role(role_id, updates)
    except ApiException as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def delete(role_id: UUID, delete_reason: str):
    """
    Delete a role (soft delete).
    
    Args:
        role_id (UUID): Role ID to delete.
        delete_reason (str): Reason for deletion.
    """
    try:
        service.delete_role(role_id, delete_reason)
        return {"message": "Role deleted successfully"}
    except ApiException as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
